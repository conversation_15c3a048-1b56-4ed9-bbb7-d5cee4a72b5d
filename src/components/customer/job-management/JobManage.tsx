
import { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  MessageSquare,
  Calendar,
  ArrowLeft,
  MapPin,
  DollarSign,
  Clock,
  Phone,
  Mail,
  AlertCircle,
  Loader2
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { apiService } from "@/services/api";

interface JobDetail {
  id: string;
  title: string;
  description: string;
  provider?: {
    name: string;
    avatar?: string;
    email?: string;
    phone?: string;
  };
  location: {
    address: string;
    city: string;
    state: string;
  };
  schedule: {
    date: string;
    time?: string;
  };
  budget: number;
  status: string;
  createdAt: string;
  service: {
    category: string;
    tasks: string[];
  };
}

export function JobManage() {
  const { jobId } = useParams<{ jobId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { token } = useAuth();

  const [job, setJob] = useState<JobDetail | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchJobDetails = useCallback(async () => {
    if (!jobId || !token) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const response = await apiService<{ data: any }>(`/api/job-bookings/${jobId}`, {
        method: 'GET',
        requiresAuth: true,
        headers: {
          'Authorization': `Bearer ${token.replace('Bearer ', '')}`
        }
      });

      if (response.isSuccess && response.data) {
        // Transform API response to match our interface
        const jobData = response.data.data;
        setJob({
          id: jobData.id,
          title: jobData.service?.tasks?.join(", ") || jobData.service?.category || 'Service Request',
          description: jobData.description || 'No description provided',
          provider: jobData.contact ? {
            name: jobData.contact.fullName || 'Provider',
            avatar: jobData.contact.avatar,
            email: jobData.contact.email,
            phone: jobData.contact.phone
          } : undefined,
          location: {
            address: jobData.location?.address || '',
            city: jobData.location?.city || '',
            state: jobData.location?.state || ''
          },
          schedule: {
            date: jobData.schedule?.date || jobData.createdAt,
            time: jobData.schedule?.time
          },
          budget: jobData.budget || 0,
          status: jobData.status || 'Active',
          createdAt: jobData.createdAt,
          service: {
            category: jobData.service?.category || 'General Service',
            tasks: jobData.service?.tasks || []
          }
        });
      }
    } catch (error) {
      console.error('Error fetching job details:', error);
      toast({
        title: "Error",
        description: "Failed to load job details",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [jobId, token, toast]);

  useEffect(() => {
    fetchJobDetails();
  }, [fetchJobDetails]);

  const handleBack = () => {
    navigate('/customer/dashboard/active-jobs');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!job) {
    return (
      <div className="space-y-6">
        <Button variant="ghost" onClick={handleBack} className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Active Jobs
        </Button>
        <Card>
          <CardContent className="py-8 text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Job not found</h3>
            <p className="text-muted-foreground">
              This job may have been removed or you don't have access to it.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Active Jobs
        </Button>
        <Badge variant={job.status === 'Active' ? 'default' : 'secondary'}>
          {job.status}
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Job Details */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-xl">{job.title}</CardTitle>
              <p className="text-muted-foreground">{job.service.category}</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Description</h4>
                <p className="text-muted-foreground">{job.description}</p>
              </div>

              {job.service.tasks.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Tasks</h4>
                  <ul className="list-disc list-inside text-muted-foreground space-y-1">
                    {job.service.tasks.map((task, index) => (
                      <li key={index}>{task}</li>
                    ))}
                  </ul>
                </div>
              )}

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Location</p>
                    <p className="text-sm text-muted-foreground">
                      {job.location.address && `${job.location.address}, `}
                      {job.location.city}, {job.location.state}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Scheduled</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(job.schedule.date)}
                      {job.schedule.time && ` at ${job.schedule.time}`}
                    </p>
                  </div>
                </div>

                {job.budget > 0 && (
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Budget</p>
                      <p className="text-sm text-muted-foreground">
                        ${job.budget.toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Posted</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(job.createdAt)}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Provider Info & Actions */}
        <div className="space-y-6">
          {job.provider ? (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Provider</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarImage src={job.provider.avatar} />
                    <AvatarFallback>
                      {job.provider.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{job.provider.name}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  {job.provider.email && (
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span>{job.provider.email}</span>
                    </div>
                  )}
                  {job.provider.phone && (
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{job.provider.phone}</span>
                    </div>
                  )}
                </div>

                <Separator />

                <div className="space-y-2">
                  <Button className="w-full" variant="outline">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Message Provider
                  </Button>
                  {job.provider.phone && (
                    <Button className="w-full" variant="outline">
                      <Phone className="h-4 w-4 mr-2" />
                      Call Provider
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Provider</CardTitle>
              </CardHeader>
              <CardContent className="py-8 text-center">
                <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">No provider assigned yet</p>
              </CardContent>
            </Card>
          )}

          {/* Job Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Job Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full" variant="outline">
                <Calendar className="h-4 w-4 mr-2" />
                Reschedule Appointment
              </Button>
              <Button className="w-full" variant="destructive">
                Cancel Job
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
