
import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  MessageSquare,
  Calendar,
  ArrowLeft,
  DollarSign,
  Star,
  Clock,
  AlertCircle,
  Loader2,
  CheckCircle,
  Phone,
  Mail
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { apiService } from "@/services/api";

interface Bid {
  id: string;
  provider: {
    name: string;
    avatar?: string;
    rating: number;
    reviewCount: number;
    email?: string;
    phone?: string;
  };
  amount: number;
  description: string;
  estimatedDuration: string;
  createdAt: string;
  status: 'pending' | 'accepted' | 'rejected';
}

interface JobDetail {
  id: string;
  title: string;
  description: string;
  status: string;
  service: {
    category: string;
    tasks: string[];
  };
}

export function JobBids() {
  const { jobId } = useParams<{ jobId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { token } = useAuth();

  const [job, setJob] = useState<JobDetail | null>(null);
  const [bids, setBids] = useState<Bid[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAccepting, setIsAccepting] = useState<string | null>(null);

  useEffect(() => {
    if (jobId) {
      fetchJobAndBids();
    }
  }, [jobId]);

  const fetchJobAndBids = async () => {
    if (!jobId || !token) return;

    setIsLoading(true);
    try {
      // Fetch job details
      const jobResponse = await apiService<{ data: any }>(`/api/job-bookings/${jobId}`, {
        method: 'GET',
        requiresAuth: true,
        headers: {
          'Authorization': `Bearer ${token.replace('Bearer ', '')}`
        }
      });

      if (jobResponse.isSuccess && jobResponse.data) {
        const jobData = jobResponse.data.data;
        setJob({
          id: jobData.id,
          title: jobData.service?.tasks?.join(", ") || jobData.service?.category || 'Service Request',
          description: jobData.description || 'No description provided',
          status: jobData.status || 'Open',
          service: {
            category: jobData.service?.category || 'General Service',
            tasks: jobData.service?.tasks || []
          }
        });
      }

      // Fetch bids for this job
      const bidsResponse = await apiService<{ data: any[] }>(`/api/job-bookings/${jobId}/bids`, {
        method: 'GET',
        requiresAuth: true,
        headers: {
          'Authorization': `Bearer ${token.replace('Bearer ', '')}`
        }
      });

      if (bidsResponse.isSuccess && bidsResponse.data) {
        // Transform bids data
        const transformedBids = bidsResponse.data.map((bid: any) => ({
          id: bid.id,
          provider: {
            name: bid.provider?.name || 'Unknown Provider',
            avatar: bid.provider?.avatar,
            rating: bid.provider?.rating || 0,
            reviewCount: bid.provider?.reviewCount || 0,
            email: bid.provider?.email,
            phone: bid.provider?.phone
          },
          amount: bid.amount || 0,
          description: bid.description || 'No description provided',
          estimatedDuration: bid.estimatedDuration || 'Not specified',
          createdAt: bid.createdAt,
          status: bid.status || 'pending'
        }));
        setBids(transformedBids);
      }
    } catch (error) {
      console.error('Error fetching job and bids:', error);
      toast({
        title: "Error",
        description: "Failed to load job bids",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAcceptBid = async (bidId: string) => {
    if (!token) return;

    setIsAccepting(bidId);
    try {
      const response = await apiService(`/api/job-bookings/${jobId}/bids/${bidId}/accept`, {
        method: 'POST',
        requiresAuth: true,
        headers: {
          'Authorization': `Bearer ${token.replace('Bearer ', '')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.isSuccess) {
        toast({
          title: "Success",
          description: "Bid accepted successfully!"
        });
        fetchJobAndBids(); // Refresh data
      }
    } catch (error) {
      console.error('Error accepting bid:', error);
      toast({
        title: "Error",
        description: "Failed to accept bid",
        variant: "destructive"
      });
    } finally {
      setIsAccepting(null);
    }
  };

  const handleBack = () => {
    navigate('/customer/dashboard/active-jobs');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!job) {
    return (
      <div className="space-y-6">
        <Button variant="ghost" onClick={handleBack} className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Active Jobs
        </Button>
        <Card>
          <CardContent className="py-8 text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Job not found</h3>
            <p className="text-muted-foreground">
              This job may have been removed or you don't have access to it.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Active Jobs
        </Button>
        <Badge variant={job.status === 'Open' ? 'default' : 'secondary'}>
          {job.status}
        </Badge>
      </div>

      {/* Job Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">{job.title}</CardTitle>
          <p className="text-muted-foreground">{job.service.category}</p>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">{job.description}</p>
        </CardContent>
      </Card>

      {/* Bids Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">
            Bids Received {bids.length > 0 && `(${bids.length})`}
          </h2>
        </div>

        {bids.length === 0 ? (
          <Card>
            <CardContent className="py-8 text-center">
              <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No bids yet</h3>
              <p className="text-muted-foreground mb-4">
                We'll notify you when professionals respond to your job request!
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 gap-4">
            {bids.map((bid) => (
              <Card key={bid.id} className="relative">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={bid.provider.avatar} />
                        <AvatarFallback>
                          {bid.provider.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">{bid.provider.name}</h3>
                        <div className="flex items-center gap-2">
                          <div className="flex">{renderStars(bid.provider.rating)}</div>
                          <span className="text-sm text-muted-foreground">
                            ({bid.provider.reviewCount} reviews)
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-green-600">
                        ${bid.amount.toLocaleString()}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {bid.estimatedDuration}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Proposal</h4>
                    <p className="text-muted-foreground">{bid.description}</p>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      <span>Submitted {formatDate(bid.createdAt)}</span>
                    </div>

                    {bid.status === 'accepted' ? (
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Accepted
                      </Badge>
                    ) : bid.status === 'rejected' ? (
                      <Badge variant="secondary">
                        Rejected
                      </Badge>
                    ) : (
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <MessageSquare className="h-4 w-4 mr-2" />
                          Message
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleAcceptBid(bid.id)}
                          disabled={isAccepting === bid.id}
                        >
                          {isAccepting === bid.id ? (
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          ) : (
                            <CheckCircle className="h-4 w-4 mr-2" />
                          )}
                          Accept Bid
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
