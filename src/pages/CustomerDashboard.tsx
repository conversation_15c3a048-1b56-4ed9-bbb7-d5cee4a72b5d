"use client";

import { cn } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Blocks, ChevronsUpDown, FileClock, GraduationCap, Layout, LayoutDashboard, LogOut, MessageSquareText, MessagesSquare, Plus, Settings, UserCircle, UserCog, UserSearch, Calendar, CheckSquare, CreditCard, Star, Shield, Gift } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Link, useLocation, useParams } from "react-router-dom";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { useIsMobile } from "@/hooks/use-mobile";
import { ResponsiveMessageInterface } from "@/components/customer/ResponsiveMessageInterface";
import { MobileCustomerLayout } from "@/components/customer/MobileCustomerLayout";

// Import the necessary components for the customer dashboard content
import { CustomerActiveJobs } from "@/components/customer/CustomerActiveJobs";
import { CustomerCompletedJobs } from "@/components/customer/CustomerCompletedJobs";
import { CustomerMessages } from "@/components/customer/CustomerMessages";
import { CustomerCalendar } from "@/components/customer/CustomerCalendar";
import { CustomerPayments } from "@/components/customer/CustomerPayments";
import { CustomerReviews } from "@/components/customer/CustomerReviews";
import { CustomerRewards } from "@/components/customer/CustomerRewards";
import { CustomerReferrals } from "@/components/customer/CustomerReferrals";
import { CustomerProfile } from "@/components/customer/CustomerProfile";
import { CustomerSettings } from "@/components/customer/CustomerSettings";
import { DashboardHome } from "@/components/customer/DashboardHome";
import { MobileCustomerDashboard } from "@/components/customer/MobileCustomerDashboard";
import { MobileActiveJobs } from "@/components/customer/job-management/MobileActiveJobs";
import { MobileCompletedJobs } from "@/components/customer/job-management/MobileCompletedJobs";
import FindProMobile from "./FindProMobile";
const sidebarVariants = {
  open: {
    width: "15rem"
  },
  closed: {
    width: "4.05rem"
  }
};
const contentVariants = {
  open: {
    display: "block",
    opacity: 1
  },
  closed: {
    display: "block",
    opacity: 1
  }
};
const variants = {
  open: {
    x: 0,
    opacity: 1,
    transition: {
      x: {
        stiffness: 1000,
        velocity: -100
      }
    }
  },
  closed: {
    x: -20,
    opacity: 0,
    transition: {
      x: {
        stiffness: 100
      }
    }
  }
};
const transitionProps = {
  type: "tween",
  ease: "easeOut",
  duration: 0.2,
  staggerChildren: 0.1
};
const staggerVariants = {
  open: {
    transition: {
      staggerChildren: 0.03,
      delayChildren: 0.02
    }
  }
};
function getTabFromSearch(search: string) {
  const params = new URLSearchParams(search);
  return params.get("tab");
}
interface CustomerSidebarProps {
  isCollapsed: boolean;
  setIsCollapsed: (collapsed: boolean) => void;
}
export function CustomerSidebar({
  isCollapsed,
  setIsCollapsed
}: CustomerSidebarProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const {
    toast
  } = useToast();
  const {
    logout,
    user
  } = useAuth();
  const isMobile = useIsMobile();

  // Get user's initials
  const getUserInitials = () => {
    if (!user?.name) return "GU";
    const names = user.name.split(' ');
    if (names.length === 1) return names[0].substring(0, 2).toUpperCase();
    return (names[0][0] + names[names.length - 1][0]).toUpperCase();
  };
  const currentTab = getTabFromSearch(location.search);
  const path = location.pathname;
  const {
    page
  } = useParams<{
    page?: string;
  }>();
  const handleLogout = () => {
    logout(); // This will handle token removal and navigation

    toast({
      title: "Logged out successfully",
      description: "You have been logged out of your account."
    });
  };
  return <motion.div className={cn("sidebar fixed left-0 z-40 h-full shrink-0 border-r")} initial={isCollapsed ? "closed" : "open"} animate={isCollapsed ? "closed" : "open"} variants={sidebarVariants} transition={transitionProps} onMouseEnter={() => setIsCollapsed(false)} onMouseLeave={() => setIsCollapsed(true)}>
        <motion.div className={`relative z-40 flex text-muted-foreground h-full shrink-0 flex-col bg-white dark:bg-gray-900 transition-all`} variants={contentVariants}>
          <motion.ul variants={staggerVariants} className="flex h-full flex-col">
            <div className="flex grow flex-col items-center">
              <div className="flex h-[64px] w-full shrink-0 border-b p-2 bg-gradient-to-br from-blue-50 to-white dark:from-gray-800 dark:to-gray-900">
                <div className="mt-1 flex w-full items-center">
                  <DropdownMenu>
                    <DropdownMenuTrigger className="w-full" asChild>
                      <Button variant="ghost" size="sm" className="flex w-fit items-center gap-2 px-2">
                        <Avatar className='size-9 border-2 border-white shadow-sm'>
                          <AvatarImage src="/placeholder.svg" alt="Customer" />
                          <AvatarFallback>JD</AvatarFallback>
                        </Avatar>
                        <motion.li variants={variants} className="flex w-fit items-center gap-2">
                          {!isCollapsed && <div className="flex flex-col items-start">
                                <p className="text-sm font-medium">
                                  {user?.name}
                                </p>
                                <span className="text-xs text-muted-foreground capitalize">{user?.role?.name}</span>
                              </div>}
                        </motion.li>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start">
                      <DropdownMenuItem asChild className="flex items-center gap-2">
                        <Link to="/customer/dashboard?tab=profile">
                          <UserCircle className="h-4 w-4" /> View Profile
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild className="flex items-center gap-2">
                        <Link to="/customer/dashboard?tab=settings">
                          <Settings className="h-4 w-4" /> Account Settings
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={handleLogout} className="flex items-center gap-2">
                        <LogOut className="h-4 w-4" /> Sign out
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>

              <div className="flex h-full w-full flex-col">
                <div className="flex grow flex-col gap-4">
                  <ScrollArea className="h-16 grow p-2">
                    <div className={cn("flex w-full flex-col gap-1")}>
                      <Link to="/customer/dashboard" className={cn("flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300", path === "/customer/dashboard" && !currentTab && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium")}>
                        <LayoutDashboard className="h-5 w-5" />
                        <motion.li variants={variants}>
                          {!isCollapsed && <p className="ml-3 text-sm font-medium">Dashboard</p>}
                        </motion.li>
                      </Link>

                      <Link to="/customer/dashboard?tab=active-jobs" className={cn("flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300", currentTab === "active-jobs" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium")}>
                        <Layout className="h-5 w-5" />
                        <motion.li variants={variants}>
                          {!isCollapsed && <p className="ml-3 text-sm font-medium">Active Jobs</p>}
                        </motion.li>
                      </Link>

                      <Link to="/customer/dashboard?tab=completed-jobs" className={cn("flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300", currentTab === "completed-jobs" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium")}>
                        <CheckSquare className="h-5 w-5" />
                        <motion.li variants={variants}>
                          {!isCollapsed && <p className="ml-3 text-sm font-medium">Completed Jobs</p>}
                        </motion.li>
                      </Link>

                      <Link to="/customer/dashboard?tab=messages" className={cn("flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300", currentTab === "messages" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium")}>
                        <MessagesSquare className="h-5 w-5" />
                        <motion.li variants={variants}>
                          {!isCollapsed && <div className="ml-3 flex items-center gap-2">
                                <p className="text-sm font-medium">Messages</p>
                                <Badge className={cn("flex h-fit w-fit items-center gap-1.5 rounded border-none bg-blue-50 px-1.5 text-blue-600 dark:bg-blue-700 dark:text-blue-300")} variant="outline">
                                  NEW
                                </Badge>
                              </div>}
                        </motion.li>
                      </Link>

                      <Link to="/customer/dashboard?tab=calendar" className={cn("flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300", currentTab === "calendar" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium")}>
                        <Calendar className="h-5 w-5" />
                        <motion.li variants={variants}>
                          {!isCollapsed && <p className="ml-3 text-sm font-medium">Calendar</p>}
                        </motion.li>
                      </Link>

                      <Separator className="my-2" />

                      <Link to="/customer/dashboard?tab=payments" className={cn("flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300", currentTab === "payments" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium")}>
                        <CreditCard className="h-5 w-5" />
                        <motion.li variants={variants}>
                          {!isCollapsed && <p className="ml-3 text-sm font-medium">Payments</p>}
                        </motion.li>
                      </Link>

                      <Link to="/customer/dashboard?tab=reviews" className={cn("flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300", currentTab === "reviews" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium")}>
                        <Star className="h-5 w-5" />
                        <motion.li variants={variants}>
                          {!isCollapsed && <p className="ml-3 text-sm font-medium">Reviews</p>}
                        </motion.li>
                      </Link>

                      <Link to="/customer/dashboard?tab=rewards" className={cn("flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300", currentTab === "rewards" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium")}>
                        <Shield className="h-5 w-5" />
                        <motion.li variants={variants}>
                          {!isCollapsed && <p className="ml-3 text-sm font-medium">Rewards</p>}
                        </motion.li>
                      </Link>

                      <Link to="/customer/dashboard?tab=referrals" className={cn("flex h-10 w-full flex-row items-center rounded-md px-2 py-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300", currentTab === "referrals" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium")}>
                        <Gift className="h-5 w-5" />
                        <motion.li variants={variants}>
                          {!isCollapsed && <p className="ml-3 text-sm font-medium">Referrals</p>}
                        </motion.li>
                      </Link>
                    </div>
                  </ScrollArea>
                </div>
                <div className="p-2 border-t bg-gradient-to-br from-white to-blue-50 dark:from-gray-900 dark:to-gray-800">
                  <Link to="/customer/dashboard?tab=settings" className={cn("flex h-10 w-full flex-row items-center rounded-md px-2 py-2 mb-2 transition hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/30 dark:hover:text-blue-300", currentTab === "settings" && "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 font-medium")}>
                    <Settings className="h-5 w-5 shrink-0" />
                    <motion.li variants={variants}>
                      {!isCollapsed && <p className="ml-3 text-sm font-medium">Settings</p>}
                    </motion.li>
                  </Link>
                  <Button variant="outline" className="w-full py-2 text-sm gap-3 font-medium rounded-md shadow-sm hover:shadow flex items-center justify-center" onClick={handleLogout}>
                    <LogOut className="h-4 w-4" />
                    {!isCollapsed && <span>Sign Out</span>}
                  </Button>
                </div>
              </div>
            </div>
          </motion.ul>
        </motion.div>
      </motion.div>;
}

// Main CustomerDashboard component that will be used in routes
const CustomerDashboard = ({
  children
}: {
  children?: React.ReactNode;
}) => {
  const location = useLocation();
  const currentTab = getTabFromSearch(location.search);
  const [isCollapsed, setIsCollapsed] = useState(true);
  const {
    page
  } = useParams<{
    page?: string;
  }>();
  const isMobile = useIsMobile();

  // Function to get the appropriate title for the current tab
  const getTabTitle = () => {
    switch (currentTab) {
      case 'active-jobs':
        return 'Active Jobs';
      case 'completed-jobs':
        return 'Completed Jobs';
      case 'messages':
        return 'Messages';
      case 'calendar':
        return 'Calendar';
      case 'payments':
        return 'Payments';
      case 'reviews':
        return 'Reviews';
      case 'rewards':
        return 'Rewards';
      case 'referrals':
        return 'Referrals';
      case 'profile':
        return 'Profile';
      case 'settings':
        return 'Settings';
      default:
        return 'Dashboard';
    }
  };

  // Function to render the appropriate content based on the current tab
  const renderContent = () => {
    if (children) {
      return children;
    }

    // Check if we're on the find-pro path
    if (page === "find-pro") {
      return <FindProMobile />;
    }

    // For mobile: return mobile-specific components when available
    if (isMobile) {
      switch (currentTab) {
        case 'active-jobs':
          return <MobileActiveJobs />;
        case 'completed-jobs':
          return <MobileCompletedJobs />;
        case 'messages':
          return <ResponsiveMessageInterface />;
        case 'payments':
          return <CustomerPayments />;
        case 'reviews':
          return <CustomerReviews />;
        case 'rewards':
          return <CustomerRewards />;
        case 'settings':
          return <CustomerSettings />;
        // Will render MobileCustomerSettings inside
        default:
          // For other tabs not yet having mobile versions, fall through to the desktop components
          break;
      }
    }

    // Return the appropriate component based on the current tab
    switch (currentTab) {
      case 'active-jobs':
        return <CustomerActiveJobs />;
      case 'completed-jobs':
        return isMobile ? <MobileCompletedJobs /> : <CustomerCompletedJobs />;
      case 'messages':
        return isMobile ? <ResponsiveMessageInterface /> : <CustomerMessages />;
      case 'calendar':
        return <CustomerCalendar />;
      case 'payments':
        return <CustomerPayments />;
      case 'reviews':
        return <CustomerReviews />;
      case 'rewards':
        return <CustomerRewards />;
      case 'referrals':
        return <CustomerReferrals />;
      case 'profile':
        return <CustomerProfile />;
      case 'settings':
        return <CustomerSettings />;
      default:
        return isMobile ? <MobileCustomerDashboard /> : <DashboardHome />;
    }
  };

  // For mobile view, use the MobileCustomerLayout
  if (isMobile) {
    return <div className="min-h-screen bg-white">
          <MobileCustomerLayout title={getTabTitle()}>
            {renderContent()}
          </MobileCustomerLayout>
        </div>;
  }
  return <div className="flex">
        <CustomerSidebar isCollapsed={isCollapsed} setIsCollapsed={setIsCollapsed} />
        <div className={cn("flex-1 h-screen overflow-auto transition-all", isCollapsed ? "pl-4" : "pl-4")}>
          <div className="container py-6 mx-auto bg-blue-50">
            {renderContent()}
          </div>
        </div>
      </div>;
};
export default CustomerDashboard;